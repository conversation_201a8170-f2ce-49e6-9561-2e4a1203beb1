const bcrypt = require('bcryptjs');

// Gerar hash da senha '<PERSON><PERSON><PERSON>@2122@'
const password = '<PERSON><PERSON><PERSON>@2122@';
const hash = bcrypt.hashSync(password, 10);

console.log('🔐 Hash da senha gerado:');
console.log(hash);

// Verificar se o hash está correto
const isValid = bcrypt.compareSync(password, hash);
console.log('✅ Hash válido:', isValid);

module.exports = { hash };
