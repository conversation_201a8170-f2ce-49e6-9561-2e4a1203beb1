export default {
  common: {
    loading: 'Loading...',
    error: 'An error occurred',
    success: 'Operation completed successfully',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View',
    search: 'Search',
    filter: 'Filter',
    close: 'Close',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    help: 'Help',
    copy: 'Copy protocol',
    copied: 'Copied!',
    done: 'Done',
    required: 'Required field',
    invalidEmail: 'Invalid email',
    invalidPhone: 'Invalid phone',
    email: 'Email',
    phone: 'Phone',
    invalidCPF: 'Invalid TAX ID',
    invalidDate: 'Invalid date',
    invalidValue: 'Invalid value',
    yes: 'Yes',
    no: 'No',
    all: 'All',
    allItems: 'All',
    none: 'None',
    select: 'Select',
    selectOption: 'Select an option',
    noResults: 'No results found',
    manage: 'Manage',
    noData: 'No data found',
    welcome: 'Welcome',
    actions: 'Actions',
    approve: 'Approve',
    reject: 'Reject',
    showing: 'Showing',
    to: 'to',
    of: 'of',
    results: 'results',
    refresh: 'Refresh',
    redirecting: 'Redirecting...',

    tryAgain: 'Try Again',
    text: 'Text',
    logout: 'Logout',
    login: 'Login',
    register: 'Register',
    password: 'Password',
    confirmPassword: 'Confirm password',
    comingSoon: 'Coming soon! This feature is under development.',
    forgotPassword: 'Forgot password?',
    resetPassword: 'Reset password',
    changePassword: 'Change password',
    newPassword: 'New password',
    currentPassword: 'Current password',
    passwordMismatch: 'Passwords do not match',
    passwordChanged: 'Password changed successfully',
    backToLogin: 'Back to Login',
    passwordExpired: 'Your password has expired and needs to be changed',
    profile: 'Profile',
    settings: 'Settings',
    language: 'Language',
    theme: 'Theme',
    dark: 'Dark',
    light: 'Light',
    system: 'System',
    chooseLanguage: 'Choose your language',
    portuguese: 'Portuguese',
    english: 'English',
    allRights: 'All',
    rights: 'rights',
    reserved: 'reserved',
    developedBy: 'Developed by',
    administrators: 'Administrators',
    managers: 'Managers',
    users: 'Users',
    accessDenied: 'Access denied',
    voltar: 'Back',

    saving: 'Saving...',
    processing: 'Processing...',
    saveSettings: 'Save Settings',
    resetPasswordFor: 'Reset Password -',
    errorResettingPassword: 'Error resetting password',
    errorSavingSettings: 'Error saving settings',
    settingsSavedSuccess: 'Settings saved successfully',
    unknownError: 'Unknown error',
    portugueseBrazil: 'Português (Brasil)',
    englishUS: 'English (US)',
    spanish: 'Español',
    notifications: 'Notifications',
    enabled: 'Enabled',
    professionalInfo: 'Professional Information',
    position: 'Position',
    department: 'Department',
    systemRole: 'System Role',
    notInformed: 'Not informed',
    preferences: 'Preferences',
    administrator: 'Administrator',
    manager: 'Manager',
    user: 'User',
    // Missing keys from Portuguese
    backToDashboard: 'Back to Dashboard',
  },
  auth: {
    phoneLogin: 'Phone Login',
    emailLogin: 'Email Login',
    email: 'Email',
    usePhone: 'Use phone',
    useEmail: 'Use email',
    enterPhone: 'Enter your phone number',
    enterEmail: 'Enter your email',
    enterCode: 'Enter verification code',
    sendCode: 'Send code',
    verifyCode: 'Verify code',
    codeSent: 'Code sent to your phone',
    codeResent: 'Code resent to your phone',
    codeSentEmail: 'Code sent to your email',
    codeResentEmail: 'Code resent to your email',
    resendCode: 'Resend code',
    codeSentAgain: 'Code resent successfully',
    resendCodeError: 'Error resending code. Please try again',
    invalidCode: 'Invalid code',
    invalidPhoneNumber: 'Please enter a valid phone number',
    invalidEmail: 'Please enter a valid email',
    codeError: 'Error sending verification code. Please try again',
    requestError: 'Error processing request. Please try again',
    loginSuccess: 'Login successful',
    loginError: 'Login error',
    sessionExpired: 'Your session has expired, please login again',
    notAuthorized: 'You are not authorized to access this page',
    welcomeBack: 'Welcome back',
    accessAccount: 'Access Your Account',
    verifyPhone: 'Verify your phone',
    enterPassword: 'Enter your password',
    pendingRequest: 'Pending Request',
    unauthorizedAccess: 'Unauthorized Access',
    pendingRequestTitle: 'Pending Request',
    pendingRequestMessage: 'Your access request is pending approval by an administrator.',
    pendingRequestNotification: 'You will receive a notification when your request is approved.',
    unauthorizedAccessTitle: 'Unauthorized Access',
    unauthorizedAccessMessage: 'You are not authorized to access the system. An access request has been created and is awaiting approval.',
    unauthorizedAccessContact: 'Contact the system administrator for more information.',
    backToStart: 'Back to start',
    inviteCode: 'Invite Code',
    hideInviteCode: 'Hide invite code',
    haveInviteCode: 'I have an invite code',
    enterInviteCode: 'Enter the invite code',
    inviteCodeHelp: 'If you received an invite code, enter it here to access the system',
    password: 'Password',
    passwordHelp: 'Enter your password to access your account',
    login: 'Login',
    rememberMe: 'Remember me',
    registeredUserMessage: 'You already have a registered account. Please enter your password to access the system.',
    invalidPassword: 'Incorrect password. Please try again',
    phoneNumber: 'Phone Number',
    phoneNumberHelp: 'Enter your phone number with country code, starting with + (e.g. +***********). The number must be in international format.',
    verificationCode: 'Verification Code',
    verificationCodeHelp: 'Enter the verification code sent to your phone',

    continue: 'Continue',
    sending: 'Sending...',
    verifying: 'Verifying...',
    loggingIn: 'Logging in...',
    forgotPassword: 'Forgot my password',
    resetPassword: 'Reset password',
    sendResetLink: 'Send reset link',
    resetLinkSent: 'Reset link sent',
    resetLinkSentEmailDescription: 'We have sent a link to reset your password to the provided email. Please check your inbox and spam folder.',
    resetLinkSentPhoneDescription: 'We have sent a link to reset your password to the provided phone number.',
    resetPasswordEmailDescription: 'You will receive an email with instructions to reset your password.',
    resetPasswordPhoneDescription: 'You will receive an SMS with instructions to reset your password.',
    passwordRequirements: 'Password must be at least 8 characters long.',
    completeRegistration: 'Complete your registration to access the system.',
    passwordMismatch: 'Passwords do not match.',
    passwordTooShort: 'Password must be at least 8 characters long.',
    passwordResetSuccess: 'Password reset successful',
    passwordResetSuccessDescription: 'Your password has been successfully reset. You will be redirected to the login page shortly.',
    resetPasswordError: 'Error resetting password',
    invalidOrExpiredToken: 'Invalid or expired link',
    tokenVerificationError: 'Error verifying reset link',
    accountLocked: 'Account locked',
    accountLockedDescription: 'Your account has been temporarily locked due to multiple failed login attempts. Please try again later.',
    newPassword: 'New password',
    confirmPassword: 'Confirm password',
    backToLogin: 'Back to login',
    emailPlaceholder: '<EMAIL>',
    backToIdentifier: 'Back to identification',
    setPasswordWithInvite: 'Set password with invite code',
    notRegistered: 'Not registered yet?',
    createAccount: 'Create account'
  },
  register: {
    title: 'Complete your registration',
    subtitle: 'Fill in the details below to create your account',
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email',
    phoneNumber: 'Phone',
    phone: 'Phone',
    cpf: 'TAX ID',
    position: 'Position',
    department: 'Department',
    password: 'Password',
    confirmPassword: 'Confirm password',
    submit: 'Register',
    loading: 'Registering...',
    success: 'Registration completed successfully',
    alreadyHaveAccount: 'Already have an account?',
    login: 'Sign in',
    error: {
      requiredFields: 'First and last name are required',
      phoneRequired: 'Phone is required',
      cpfRequired: 'TAX ID is required',
      cargoRequired: 'Position is required',
      invalidCpf: 'TAX ID must have 11 digits',
      generic: 'Error during registration'
    },
    requiredFields: 'Fields marked with * are required',
    firstNamePlaceholder: 'Your first name',
    lastNamePlaceholder: 'Your last name',
    emailPlaceholder: '<EMAIL>',
    phoneNumberPlaceholder: '+****************',
    phonePlaceholder: '+****************',
    cpfPlaceholder: '000.000.000-00',
    positionPlaceholder: 'e.g. Developer',
    departmentPlaceholder: 'e.g. Technology',
    passwordPlaceholder: 'Minimum 8 characters',
    confirmPasswordPlaceholder: 'Repeat password',
    completeRegistration: 'Complete your registration to access the system',
    registrationIncomplete: 'Incomplete registration',
    registrationSuccessRedirect: 'Registration completed! Redirecting to dashboard...',
    protocolNumber: 'Protocol Number',
    backToLogin: 'Back to Login',
    successMessage: 'Your registration has been completed successfully. Please check your email to confirm your account.',
    successMessageActive: 'Your registration has been completed successfully and your account is already active.',
    loginNow: 'You can now log in immediately with your email or phone.',
    checkEmail: 'Please check your email to activate your account.',
    notRegisteredYet: 'This email/phone is not yet registered. Please complete your registration below.',
  },
  viewer: {
    loading: 'Loading document...',
    error: 'Error loading document',
    downloadView: 'Download and view locally',
    browserNotSupported: 'Your browser does not support PDF viewing',
    downloadPrompt: 'Please download the document to view it',
    downloadPdf: 'Download PDF',
    showPdf: 'View PDF',
    showContent: 'View extracted content',
    extracting: 'Extracting document content...',
    extractError: 'Error extracting content',
    noContent: 'No content extracted',
    zoomIn: 'Zoom in',
    zoomOut: 'Zoom out',
    rotate: 'Rotate',
    fileNotFound: 'File not found',
    fitWidth: 'Fit to width',
    width: 'Width',
    fitHeight: 'Fit to height',
    height: 'Height',
    fitPage: 'Fit to page',
    page: 'Page',
    download: 'Download document',
    fullscreen: 'Fullscreen',
    exitFullscreen: 'Exit fullscreen',
    close: 'Close viewer',
  },
  dashboard: {
    title: 'Dashboard',
    welcome: 'Welcome to ABZ Dashboard',
    quickAccess: 'Quick Access',
    recentDocuments: 'Recent Documents',
    announcements: 'Announcements',
    statistics: 'Statistics',
    tasks: 'Tasks',
    calendar: 'Calendar',
    notifications: 'Notifications',
    noNotifications: 'No notifications',
    viewAll: 'View all',
    logisticsPanel: 'ABZ Group Logistics Dashboard',
    welcomeMessage: 'Welcome to the resource center for logistics staff.',
    quickAccessFeatures: 'Quick Access to Features',
    centralizedPanel: 'This dashboard centralizes all necessary resources for the logistics sector.',
    contactSupport: 'For more information or support, please contact the IT department.',
    accessAdminPanel: 'Access Admin Panel',
    access: 'Access',
    noCards: 'No cards available',
  },
  menu: {
    dashboard: 'Dashboard',
    manualLogistico: 'Logistics Manual',
    procedimentoLogistica: 'Logistics Procedure',
    politicas: 'Policies',
    procedimentosGerais: 'General Procedures',
    calendario: 'Calendar',
    abzNews: 'ABZ News',
    reembolso: 'Reimbursement',
    contracheque: 'Payslip',
    ponto: 'Time Clock',
    folhaPagamento: 'Payroll',
    avaliacao: 'Performance Evaluation',
    administracao: 'Administration',
    usuariosAutorizados: 'Authorized Users',
  },
  cards: {
    // Traduções padrão para os cards do menu
    manualColaborador: 'Employee Manual',
    manualColaboradorDesc: 'Access the complete employee manual.',
    procedimentosLogistica: 'Logistics Procedures',
    procedimentosLogisticaDesc: 'Check the standard procedures for the area.',
    politicas: 'Policies',
    politicasDesc: 'Check company policies.',
    procedimentosGerais: 'General Procedures',
    procedimentosGeraisDesc: 'Check the company general procedures.',
    calendario: 'Calendar',
    calendarioDesc: 'View important events and dates.',
    noticias: 'News',
    noticiasDesc: 'Stay up to date with company news.',
    reembolso: 'Reimbursement',
    reembolsoDesc: 'Request expense reimbursement.',
    contracheque: 'Payslip',
    contrachequeDesc: 'Access your payslips.',
    ponto: 'Time Clock',
    pontoDesc: 'Register your time and check your history.',
    avaliacao: 'Performance Evaluation',
    avaliacaoDesc: 'Manage employee performance evaluations.',
    folhaPagamento: 'Payroll',
    folhaPagamentoDesc: 'Complete payroll management and labor calculations.',
    admin: 'Admin',
    adminDesc: 'Administrative panel',

    // Traduções para os cards do banco de dados (por ID)
    // Versão sem hífens (compatibilidade)
    '6377431f4afa448bb46a8321a5870f37': 'Manual',
    '6377431f4afa448bb46a8321a5870f37Desc': 'Access the company manual',
    'c40a97fd70a543f1af4d960efabd340b': 'Procedures',
    'c40a97fd70a543f1af4d960efabd340bDesc': 'Check company procedures',
    '2285fcbd70244f9a91c3e0a87de27ba0': 'Policies',
    '2285fcbd70244f9a91c3e0a87de27ba0Desc': 'Check company policies',
    '90e09b57c23e41499770e35c2d66cf7a': 'Calendar',
    '90e09b57c23e41499770e35c2d66cf7aDesc': 'View the events calendar',
    '01aa36f2d02e49ab903cf9a638b2f0ba': 'News',
    '01aa36f2d02e49ab903cf9a638b2f0baDesc': 'Stay up to date with the latest news',
    '3e7b2395d70847de83aba8a7f61d0977': 'Reimbursement',
    '3e7b2395d70847de83aba8a7f61d0977Desc': 'Request expense reimbursement',
    '64ee7490519f42d2afbaf3063bbe1bdc': 'Payslip',
    '64ee7490519f42d2afbaf3063bbe1bdcDesc': 'Access your payslips',
    '515e6360431d43b69877a1d0ca23296c': 'Time Clock',
    '515e6360431d43b69877a1d0ca23296cDesc': 'Register your time',
    '5b07e529830c43be8f75dff38053744c': 'Evaluation',
    '5b07e529830c43be8f75dff38053744cDesc': 'Access your evaluations',
    'e460055d4b674350a0155317fc07e76a': 'Admin',
    'e460055d4b674350a0155317fc07e76aDesc': 'Administrative panel',

    // Versão com hífens (formato do banco de dados)
    '6377431f-4afa-448b-b46a-8321a5870f37': 'Manual',
    '6377431f-4afa-448b-b46a-8321a5870f37Desc': 'Access the company manual',
    'c40a97fd-70a5-43f1-af4d-960efabd340b': 'Procedures',
    'c40a97fd-70a5-43f1-af4d-960efabd340bDesc': 'Check company procedures',
    '2285fcbd-7024-4f9a-91c3-e0a87de27ba0': 'Policies',
    '2285fcbd-7024-4f9a-91c3-e0a87de27ba0Desc': 'Check company policies',
    '90e09b57-c23e-4149-9770-e35c2d66cf7a': 'Calendar',
    '90e09b57-c23e-4149-9770-e35c2d66cf7aDesc': 'View the events calendar',
    '01aa36f2-d02e-49ab-903c-f9a638b2f0ba': 'News',
    '01aa36f2-d02e-49ab-903c-f9a638b2f0baDesc': 'Stay up to date with the latest news',
    '3e7b2395-d708-47de-83ab-a8a7f61d0977': 'Reimbursement',
    '3e7b2395-d708-47de-83ab-a8a7f61d0977Desc': 'Request expense reimbursement',
    '64ee7490-519f-42d2-afba-f3063bbe1bdc': 'Payslip',
    '64ee7490-519f-42d2-afba-f3063bbe1bdcDesc': 'Access your payslips',
    '515e6360-431d-43b6-9877-a1d0ca23296c': 'Time Clock',
    '515e6360-431d-43b6-9877-a1d0ca23296cDesc': 'Register your time',
    '5b07e529-830c-43be-8f75-dff38053744c': 'Evaluation',
    '5b07e529-830c-43be-8f75-dff38053744cDesc': 'Access your evaluations',
    'e460055d-4b67-4350-a015-5317fc07e76a': 'Admin',
    'e460055d-4b67-4350-a015-5317fc07e76aDesc': 'Administrative panel',
  },
  ponto: {
    title: 'Time Clock',
    pageTitle: 'Time Clock',
    description: 'Register your time and check your history',
    welcomeTitle: 'Welcome to Batida Online',
    welcomeDescription: 'Batida Online is the platform used by ABZ Group for employee time tracking, automating processes and making day-to-day operations easier.',
    accessBatidaOnline: 'Access Batida Online (Web)',
    appAccess: 'App Access',
    ahgoraMultiDescription: 'Use this app to register your time clock via facial recognition (according to company settings).',
    activationKey: 'Activation Key:',
    activationKeyInfo: '(Provided in the email sent by HR)',
    appStoreUnavailable: 'App Store link unavailable',
    myAhgoraDescription: 'Use this app to check your time clock records, request adjustments and absences.',
    companyCode: 'Company Code:',
    registrationPassword: 'Registration/Password:',
    webAccess: 'Web Access',
    webAccessDescription: 'You can also access the time clock system through your browser.',
    additionalResources: 'Additional Resources',
    manualDescription: 'Check the user manual for detailed instructions on how to use the Ahgora platforms.',
    downloadManual: 'Download Manual (PDF)',
  },

  procedimentos: {
    title: 'General Procedures',
    pageTitle: 'General Procedures',
    comingSoon: 'Coming Soon',
    description: 'This section will host general procedures and guidelines from various departments.',
    contentAvailable: 'Content will be added as soon as it becomes available.',
  },

  contracheque: {
    title: 'Payslip Consultation',
    pageTitle: 'Payslip Consultation',
    description: 'Access your payslips and salary information',
    accessSystem: 'Access External System',
    systemDescription: 'Click the button below to access the payslip consultation system.',
    externalAccess: 'You will be redirected to an external system to view your payslips.',
  },

  calendario: {
    title: 'Holiday Calendar',
    description: 'National and UK Bank Holidays',
    loading: 'Loading holidays...',
    loadingHolidays: 'Loading...',
    couldNotLoadHolidays: 'Could not load holidays.',
    noHolidaysThisMonth: 'No holidays this month.',
    nationalHoliday: 'National / Other Holiday',
    municipalHoliday: 'UK Bank Holiday',
    holidaysInMonth: 'Holidays in',
    failedToFetchBrasilApi: 'Failed to fetch from BrasilAPI',
    tryingAlternative: 'Trying alternative...',
    failedToFetchFromAllSources: 'Failed to fetch holidays from all sources.',
  },
  reimbursement: {
    title: 'Reimbursement',
    description: 'Request expense reimbursement',
    tabs: {
      request: 'Request Reimbursement',
      dashboard: 'My Reimbursements',
      approval: 'Approve Reimbursements'
    },
    loadingDashboard: 'Loading reimbursement dashboard...',
    loadingApproval: 'Loading reimbursement approvals...',
    goToDashboard: 'Go to reimbursement dashboard',
    goToApproval: 'Go to reimbursement approvals',
    redirectingToDashboard: 'Redirecting to reimbursement dashboard...',
    redirectingToApproval: 'Redirecting to reimbursement approvals...',
    approvalPermissionRequired: 'You do not have permission to access the reimbursement approval page.',
    form: {
      title: 'Reimbursement Form',
      personalInfo: 'Personal Information',
      fullName: 'Full Name',
      email: 'Email',
      emailLocked: 'Email locked (logged in user)',
      phone: 'Phone',
      cpf: 'Tax ID (CPF)',
      position: 'Position',
      costCenter: 'Cost Center',
      date: 'Date',
      expenseDate: 'Expense Date',
      expenseType: 'Expense Type',
      reimbursementType: 'Reimbursement Type',
      description: 'Description',
      descriptionPlaceholder: 'Describe the expense...',
      amount: 'Amount',
      totalValue: 'Total Value',
      currency: 'Currency',
      paymentMethod: 'Payment Method',
      bankName: 'Bank Name',
      bankInfo: 'Bank Information',
      agency: 'Agency',
      account: 'Account',
      pixType: 'PIX Type',
      pixKeyType: 'PIX Key Type',
      pixKey: 'PIX Key',
      observations: 'Observations',
      addReceipt: 'Add Receipt',
      submit: 'Submit Request',
      submitting: 'Submitting...',
      expenses: 'Expenses',
      expense: 'Expense',
      paymentInfo: 'Payment Information',
      receipts: 'Receipts',
      submitSuccess: 'Request submitted successfully',
      submitError: 'Error submitting request',
      protocol: 'Protocol',
      thankYou: 'Thank you for your request',
      thankYouMessage: 'Your request has been received and will be processed shortly.',
      viewPolicy: 'View Reimbursement Policy',
      notes: 'Notes (optional)',
      pixKeyPlaceholder: 'Enter your PIX key',
      pixCpfPlaceholder: '000.000.000-00 (TAX ID)',
      pixEmailPlaceholder: '<EMAIL>',
      pixPhonePlaceholder: '(00) 00000-0000',
      pixRandomPlaceholder: 'Random PIX key',
      costCenterRequired: 'Cost center is required',
      authStatus: {
        loginRequired: 'Login required',
        loginRequiredMessage: 'You need to be logged in to send a reimbursement.',
        loginLink: 'Click here to login',
        authenticated: 'You are logged in and can send reimbursements',
        redirectingToLogin: 'You need to be logged in to send a reimbursement. Redirecting to login...',
      },
    },
    policy: {
      title: 'Reimbursement Policy',
      introduction: 'This policy establishes guidelines for requesting and approving corporate expense reimbursements.',
      eligibility: 'Eligibility',
      eligibilityText: 'All registered employees can request reimbursements for work-related expenses.',
      typesTitle: 'Eligible Expense Types',
      foodLabel: 'Food:',
      foodText: 'Meals during business trips or business meetings.',
      transportLabel: 'Transportation:',
      transportText: 'Tickets, fuel, parking, and other transportation costs.',
      accommodationLabel: 'Accommodation:',
      accommodationText: 'Hotels and other accommodations during business trips.',
      materialsLabel: 'Materials:',
      materialsText: 'Office supplies and materials necessary for work.',
      otherLabel: 'Other:',
      otherText: 'Other pre-approved work-related expenses.',
      documentationTitle: 'Required Documentation',
      documentationText: 'All reimbursements must include valid receipts containing:',
      docDate: 'Expense date',
      docAmount: 'Exact amount',
      docDescription: 'Clear description of item/service',
      docSupplier: 'Supplier name',
      deadlinesTitle: 'Deadlines',
      deadlinesText: 'Requests must be submitted within 30 days of the expense.',
      paymentTitle: 'Payment Methods',
      paymentText: 'Reimbursements can be processed through:',
      paymentBank: 'Bank deposit',
      paymentPix: 'PIX',
      paymentCash: 'Cash (small amounts only)',
      restrictionsTitle: 'Restrictions',
      restrictionsText: 'The following expenses are NOT eligible for reimbursement:',
      restrictionProof: 'Expenses without valid receipt',
      restrictionPersonal: 'Personal expenses',
      restrictionAuth: 'Expenses not previously authorized',
      restrictionPolicy: 'Expenses that violate company policies',
      restrictionDoc: 'Illegible or incomplete documents',
      finalTitle: 'Final Approval',
      finalText: 'All requests are subject to management approval and may be rejected if they do not meet established criteria.',
      agreeButton: 'I Have Read and Agree',
    },
    types: {
      alimentacao: 'Food',
      transporte: 'Transportation',
      hospedagem: 'Accommodation',
      combustivel: 'Fuel',
      outros: 'Others'
    },
    paymentMethods: {
      deposito: 'Bank Deposit',
      pix: 'PIX'
    },
    pixTypes: {
      cpf: 'CPF',
      email: 'Email',
      telefone: 'Phone',
      chaveAleatoria: 'Random Key'
    },
    costCenters: {
      abz: 'ABZ',
      ahn: 'AHK'
    },
    rejectionReasonRequired: 'Rejection reason is required',
    confirmRejection: 'Confirm Rejection',
    rejectionReasonPlaceholder: 'Enter rejection reason...',
    modal: {
      title: 'Reimbursement Details',
      applicantInfo: 'Applicant Information',
      reimbursementInfo: 'Reimbursement Information',
      description: 'Description',
      paymentMethod: 'Payment Method',
      receipts: 'Receipts',
      history: 'History',
      rejectionReason: 'Rejection Reason',
      rejectionReasonDescription: 'Please provide the reason for rejection. This information will be sent to the applicant.',
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      cpf: 'TAX ID',
      position: 'Position',
      costCenter: 'Cost Center',
      protocol: 'Protocol',
      type: 'Type',
      value: 'Value',
      date: 'Date',
      status: 'Status',
      method: 'Method',
      bank: 'Bank',
      agency: 'Agency',
      account: 'Account',
      pixType: 'PIX Type',
      pixKey: 'PIX Key',
      notInformed: 'Not informed',
      downloadFile: 'Download file',
      approve: 'Approve',
      reject: 'Reject',
      cancel: 'Cancel',
      close: 'Close'
    }
  },
  manual: {
    title: 'Employee Manual (Logistics)',
    description: 'Complete guide with guidelines and information about logistics processes.',
    download: 'Download (PDF)',
    view: 'View',
    noManuals: 'No manuals found',
    mainDocument: 'Main Document',
  },
  procedures: {
    title: 'Logistics Procedures (Revised)',
    description: 'Detailed document with revised standard operating procedures for the area.',
    download: 'Download (PDF)',
    view: 'View',
    noProcedures: 'No procedures found',
    mainDocument: 'Main Document',
  },
  policies: {
    title: 'Policies',
    description: 'Company policies',
    download: 'Download',
    view: 'View',
    noPolicies: 'No policies found',
    documentsTitle: 'Policy Documents',
    hse: {
      title: 'HSE Policy',
      description: 'Health, Safety and Environment Guidelines of ABZ Group'
    },
    quality: {
      title: 'Quality Policy',
      description: 'ABZ Group Quality and Management Policy',
      titleEn: 'Quality Policy',
      descriptionEn: 'ABZ Group Quality Management Policy',
      category: 'Quality'
    },
  },
  news: {
    title: 'ABZ News and Announcements',
    description: 'Latest company news',
    readMore: 'Read more',
    noNews: 'No news or announcements available at the moment.',
    publishedAt: 'Published on',
    by: 'by',
    view: 'View',
    download: 'Download (PDF)',
    examples: {
      title1: 'News Example 1',
      description1: 'Brief description of the content of this news or booklet.',
      title2: 'News Example 2',
      description2: 'Another example of an important announcement in PDF format.'
    }
  },
  locale: {
    code: 'en-US'
  },


  admin: {
    title: 'Administration',
    dashboard: 'Administration Panel',
    systemSetup: 'System Setup',
    welcomeAdmin: 'Welcome, {name}. Manage system content and settings.',
    cards: 'Cards',
    cardsDesc: 'Manage cards displayed on the main dashboard.',
    menu: 'Menu',
    menuDesc: 'Configure sidebar menu items.',
    documentsSection: 'Documents',
    documentsDesc: 'Manage documents, policies, and manuals.',
    news: 'News',
    newsDesc: 'Add and edit news and announcements.',
    addNews: 'Add News',
    editNews: 'Edit News',
    errorLoadingNews: 'Error loading news. Please try again.',
    errorSavingNews: 'Error saving news. Please try again.',
    errorDeletingNews: 'Error deleting news. Please try again.',
    noNewsFound: 'No news found. Click "Add News" to create a new one.',
    addMenuItem: 'Add Menu Item',
    noItems: 'No items found. Click "Add Item" to create a new one.',
    addNewEmail: 'Add new email',
    refresh: 'Refresh',
    uploading: 'Uploading...',
    upload: 'Upload',
    addAuthorization: 'Add Authorization',
    generateCode: 'Generate Code',
    refreshUserList: 'Refresh user list',
    cardsTable: 'Cards Table',
    cardsTableDescription: 'This utility allows you to create the cards table in the Supabase database. The table is necessary to store dashboard cards.',
    creatingCardsTable: 'Creating cards table...',
    errorCreatingCardsTable: 'Error creating cards table',
    cardsTableCreatedSuccess: 'Cards table created successfully',
    checkingCardsTable: 'Checking cards table...',
    errorCheckingCardsTable: 'Error checking cards table',
    cardsTableExists: 'The cards table already exists in the database',
    cardsTableNotExists: 'The cards table does not exist. Click the button to create it.',
    createCardsTable: 'Create Cards Table',
    checkTable: 'Check Table',
    cardsMigration: 'Cards Migration',
    cardsMigrationDescription: 'This utility allows you to migrate hardcoded cards from the source code to the Supabase database. This allows cards to be edited through the administrative interface.',
    checkingMigrationStatus: 'Checking migration status...',
    errorCheckingMigrationStatus: 'Error checking migration status',
    migrationStatusCheckedSuccess: 'Migration status checked successfully',
    migratingCards: 'Migrating cards to database...',
    errorMigratingCards: 'Error migrating cards',
    cardsMigratedSuccess: 'Cards migrated successfully',
    migrateCards: 'Migrate Cards',
  },
  fileUploader: {
    attachments: 'Attachments',
    dragDropText: 'Drag and drop files here, or click to select',
    acceptedFormats: 'Accepted formats: PDF, JPG, PNG (max. {maxSize}MB per file)',
    selectedFiles: 'Selected files ({count}/{max})',
    uploading: 'Uploading...',
    unsupportedFileType: 'Unsupported file type: {type}',
    fileTooLarge: 'File too large: {size}MB (maximum: {maxSize}MB)',
    maxFilesExceeded: 'You can upload a maximum of {maxFiles} files',
    errorReadingFile: 'Error reading file',
    fileStoredLocally: 'File stored locally due to storage issues. The file will be included in your submission, but may not be permanently stored.',
    uploadError: 'Upload error',
    errorUploadingFile: 'Error uploading {fileName}: {error}',
  },
  starRating: {
    ariaLabel: '{star} of {maxRating} stars',
  },
  evaluation: {
    noPermission: 'You do not have permission to access the evaluation module.',
    errorLoadingEvaluations: 'Error loading evaluations',
    viewNotConfigured: 'The evaluation view is not configured. Contact the administrator.',
    noPermissionAccess: 'You do not have permission to access evaluations.',
    evaluator: 'Evaluator',
    period: 'Period',
    dashboard: {
      title: 'Performance Evaluations',
    },
    status: {
      pending: 'Pending',
      inProgress: 'In Progress',
      completed: 'Completed',
      cancelled: 'Cancelled',
    },
  },
  userEditor: {
    newUser: 'New User',
    editUser: 'Edit User',
    requiredFields: 'Phone number, first name and last name are required',
    passwordRequired: 'Password is required for new users',
    passwordMismatch: 'Passwords do not match',
    usersSection: 'User Management',
    usersSectionDesc: 'Manage users, permissions and access authorizations.',
    authorizedUsers: 'Authorized Users',
    authorizedUsersDesc: 'Manage access requests and authorized users.',
    settings: 'Settings',
    settingsDesc: 'Configure system options.',
    permissions: 'Permissions',
    adminOnly: 'Admin Only',
    managerOnly: 'Manager Only',
    allowedRoles: 'Allowed Roles',
    allowedUsers: 'Allowed Users',
    addUser: 'Add User',
    searchUsers: 'Search users...',
    noUsersFound: 'No users found',
    roleUser: 'Standard User',
    roleManager: 'Manager',
    roleAdmin: 'Administrator',
    newsTitle: 'News Title',
    newsDescription: 'Brief Description',
    newsContent: 'Content',
    category: 'Category',
    categoryGeneral: 'General',
    categoryAnnouncement: 'Announcement',
    categoryEvent: 'Event',
    categoryUpdate: 'Update',
    author: 'Author',
    date: 'Date',
    featured: 'Featured',
    enabled: 'Enabled',
    thumbnail: 'Thumbnail',
    thumbnailDescription: 'Small image for news listing',
    coverImage: 'Cover Image',
    coverImageDescription: 'Large image for news detail page',
    attachment: 'Attachment',
    attachmentDescription: 'Optional file to attach to the news',
    chooseImage: 'Choose Image',
    chooseFile: 'Choose File',
    tags: 'Tags',
    addTag: 'Add tag...',
    editNews: 'Edit News',
    addNews: 'Add News',
    fieldRequired: 'This field is required',
    cardManagement: 'Card Management',
    cardManagementDesc: 'Add, edit, or remove cards displayed on the dashboard.',
    addCard: 'Add Card',
    menuManagement: 'Menu Management',
    menuManagementDesc: 'Add, edit, or remove sidebar menu items.',
    addMenuItem: 'Add Item',
    noItems: 'No items found. Click "Add Item" to create a new one.',
    systemInfo: 'System Information',
    version: 'Version',
    environment: 'Environment',
    production: 'Production',
    lastUpdate: 'Last Update',
    database: 'Database',
    status: 'Status',
    online: 'Online',
    offline: 'Offline',
    activeUsers: 'Active Users',
    lastLogin: 'Last Login',
    active: 'Active',
    inactive: 'Inactive',
    manage: 'Manage',
    users: {
      title: 'Users',
      addUser: 'Add User',
      editUser: 'Edit User',
      deleteUser: 'Delete User',
      deleteConfirm: 'Are you sure you want to delete this user?',
      userDeleted: 'User deleted successfully',
      userAdded: 'User added successfully',
      userUpdated: 'User updated successfully',
      userDetails: 'User Details',
      personalInfo: 'Personal Information',
      firstName: 'First Name',
      lastName: 'Last Name',
      email: 'Email',
      phone: 'Phone',
      role: 'Role',
      admin: 'Administrator',
      manager: 'Manager',
      user: 'User',
      department: 'Department',
      position: 'Position',
      status: 'Status',
      active: 'Active',
      inactive: 'Inactive',
      createdAt: 'Created at',
      updatedAt: 'Updated at',
      lastLogin: 'Last login',
      resetPassword: 'Reset Password',
      passwordReset: 'Password reset successfully',
      accessHistory: 'Access History',
      noHistory: 'No history found',
      permissions: 'Permissions',
      modules: 'Modules',
      features: 'Features',
    },
    documents: {
      section: 'Documents',
      description: 'Add, edit, or remove documents, policies, and manuals.',
      addDocument: 'Add Document',
      editDocument: 'Edit Document',
      deleteDocument: 'Delete Document',
      deleteConfirm: 'Are you sure you want to delete this document?',
      documentDeleted: 'Document deleted successfully',
      documentAdded: 'Document added successfully',
      documentUpdated: 'Document updated successfully',
      documentDetails: 'Document Details',
      docTitle: 'Title',
      category: 'Category',
      language: 'Language',
      file: 'File',
      filePlaceholder: 'File path or URL',
      select: 'Select',
      active: 'Active',
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      moveUp: 'Move Up',
      moveDown: 'Move Down',
      enable: 'Enable',
      disable: 'Disable',
      download: 'Download',
      filterByCategory: 'Filter by Category',
      all: 'All',
      loading: 'Loading documents...',
      noItems: 'No documents found. Click "Add Document" to create a new one.',
      selectLanguage: 'Select a language',
      portuguese: 'Portuguese',
      english: 'English',
      spanish: 'Spanish',
      order: 'Order',
      errorLoading: 'Error loading documents',
      errorLoadingMessage: 'Error loading documents. Please try again.',
      uploadFile: 'Upload file',
      replaceFile: 'Replace file',
      enabled: 'Enabled',
      disabled: 'Disabled',

      createdAt: 'Created at',
      updatedAt: 'Updated at',
    },
  },
  contact: {
    title: 'Contact',
    description: 'Contact us',
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    message: 'Message',
    send: 'Send',
    sending: 'Sending...',
    success: 'Message sent successfully',
    error: 'Error sending message',
    needHelp: 'Need help?',
    helpMessage: 'If you have questions about the reimbursement form or need assistance, please contact us:',
    businessHours: 'Business Hours',
    businessHoursTime: 'Monday to Friday, 9am to 6pm',
  },
  errors: {
    notFound: 'Page not found',
    serverError: 'Internal server error',
    unauthorized: 'Unauthorized',
    forbidden: 'Access denied',
    badRequest: 'Bad request',
    conflict: 'Conflict',
    validation: 'Validation error',
    timeout: 'Timeout',
    offline: 'You are offline',
    unknown: 'Unknown error',
    pageNotFound: 'Page not found',
    pageNotFoundMessage: 'The page you are looking for does not exist or has been moved.',
    errorOccurred: 'An error occurred',
    somethingWentWrong: 'Something went wrong. Please try again.',
    errorCode: 'Error code',
  },
  manager: {
    moduleTitle: 'Manager Module',
    moduleDescription: 'Special tools and features for managers',
    welcome: 'Welcome to the Manager Module',
    moduleIntro: 'This module provides special tools and features designed specifically for managers. Here you can access advanced reports, team management tools, and other resources to help you manage your team effectively.',
  },
  avaliacao: {
    title: 'Performance Evaluation',
    description: 'Manage employee performance evaluations',
    dashboard: 'Dashboard',
    moduleStatus: 'Module status',
    statusLoading: 'Loading...',
    statusOnline: 'Online',
    statusError: 'Error',
    novaAvaliacao: 'New Evaluation',
    novaAvaliacaoDesc: 'Create a new performance evaluation',
    nova: {
      loadFuncionariosError: 'An error occurred while loading employees. Please try again.',
      requiredFieldsError: 'Please fill in all required fields.',
      createError: 'An error occurred while creating the evaluation. Please try again.',
      createSuccess: 'Evaluation created successfully! Redirecting...',
      selectAvaliador: 'Select an evaluator',
      selectFuncionario: 'Select an employee'
    },
    observacoes: 'Notes',
    editAvaliacao: 'Edit Evaluation',
    redirectingToList: 'Redirecting to the evaluation list in a few seconds...',
    avaliacoes: {
      title: 'Evaluations',
      description: 'Manage performance evaluations',
      empty: 'No evaluations registered',
      emptyDesc: 'Start by creating a new performance evaluation.'
    },
    searchPlaceholder: 'Search evaluations...',
    trashLink: 'Trash',
    movedToTrashSuccess: 'Evaluation moved to trash successfully!',
    moveToTrashError: 'An error occurred while moving the evaluation to trash. Please try again.',
    confirmMoveToTrash: 'Are you sure you want to move this evaluation to trash? It will be permanently deleted after 30 days.',
    trash: {
      title: 'Evaluation Trash',
      backToList: 'Back to list',
      searchPlaceholder: 'Search evaluations in trash...',
      empty: 'No evaluations in trash.',
      loadError: 'An error occurred while loading evaluations from trash. Please try again.',
      confirmRestore: 'Are you sure you want to restore this evaluation?',
      restoreSuccess: 'Evaluation restored successfully!',
      restoreError: 'An error occurred while restoring the evaluation. Please try again.',
      confirmDelete: 'Are you sure you want to permanently delete this evaluation? This action cannot be undone.',
      deleteSuccess: 'Evaluation permanently deleted!',
      deleteError: 'An error occurred while deleting the evaluation. Please try again.',
      deletedAt: 'Deleted at',
      restore: 'Restore',
      deletePermanently: 'Delete permanently'
    },
    createdAt: 'Creation Date',
    actions: 'Actions',
    status: {
      title: 'Status',
      pending: 'Pending',
      inProgress: 'In Progress',
      completed: 'Completed',
      archived: 'Archived'
    },
    debug: {
      title: 'Evaluation Debugging',
      description: 'Tool to identify issues in the evaluation route',
      systemDiagnostic: 'System Diagnostic',
      authStatus: 'Authentication Status',
      tokenStatus: 'Token Status',
      routerStatus: 'Router Status',
      apiStatus: 'API Status',
      apiData: 'API Data',
      navigationLinks: 'Navigation Links',
      directLinks: 'Direct Links (window.location)',
      routerLinks: 'Links via Router',
      linkComponentLinks: 'Links via Link Component',
      dashboardLink: 'Evaluation Dashboard',
      evaluationListLink: 'Evaluation List'
    },
    funcionarios: {
      title: 'Employees',
      noFuncionarios: 'No employees registered',
      addFuncionario: 'Add Employee',
      detalhes: 'Employee Details',
      detalhesDesc: 'View employee information and evaluations',
      funcionarioNaoEncontrado: 'Employee not found',
      nome: 'Name',
      cargo: 'Position',
      departamento: 'Department',
      dataAdmissao: 'Admission Date',
      email: 'Email',
      telefone: 'Phone',
      status: 'Status'
    },
    relatorios: {
      title: 'Reports',
      description: 'Generate performance evaluation reports',
      tipoRelatorio: 'Report Type',
      periodoRelatorio: 'Period',
      tipoOptions: {
        individual: 'Individual',
        departamento: 'By Department',
        geral: 'General'
      },
      exportarExcel: 'Export Excel',
      exportarPDF: 'Export PDF',
      gerarRelatorio: 'Generate Report'
    },
    importacao: {
      title: 'Import',
      description: 'Import performance evaluation data',
      instructionsText: 'Drag and drop a CSV file or click to select',
      fileFormat: 'Supported formats: CSV, XLS, XLSX',
      selectFile: 'Select File',
      template: 'Download Template',
      upload: 'Import Data',
      processing: 'Processing...',
      success: 'Import completed successfully',
      rowsProcessed: 'Rows processed',
      rowsImported: 'Rows imported',
      rowsSkipped: 'Rows skipped',
      rowsWithErrors: 'Rows with errors',
      preview: 'Preview',
      instructions: 'Instructions',
      importAvaliacoes: 'Import Evaluations',
      importFuncionarios: 'Import Employees',
      importCriterios: 'Import Criteria',
      invalidFileType: 'Invalid file type. Please select a CSV, XLSX or XLS file.'
    },
    periodoOptions: {
      mensal: 'Monthly',
      trimestral: 'Quarterly',
      semestral: 'Biannual',
      anual: 'Annual'
    },
    funcionario: 'Employee',
    avaliador: 'Evaluator',
    periodo: 'Period',
    dataInicio: 'Start Date',
    dataFim: 'End Date',

    criterios: 'Evaluation Criteria',
    semCriterios: 'No evaluation criteria available',
    historicoAvaliacoes: 'Evaluation History',
    semAvaliacoes: 'This employee has no evaluations registered yet',
    noAvaliacoes: 'No evaluations registered',
    avaliacaoGerenteLogado: 'You are creating this evaluation as the evaluator manager'
  },
  payroll: {
    title: 'Payroll',
    description: 'Complete payroll management',
    newPayroll: 'New Payroll',
    quickActions: 'Quick Actions',
    newPayrollSheet: 'New Payroll Sheet',
    manageEmployees: 'Manage Employees',
    manageCompanies: 'Manage Companies',
    reports: 'Reports',
    monthlyReport: 'Monthly Report',
    paymentGuides: 'Payment Guides',
    costAnalysis: 'Cost Analysis',
    settings: 'Settings',
    payrollCodes: 'Payroll Codes',
    calculationProfiles: 'Calculation Profiles',
    legalTables: 'Legal Tables',
    recentSheets: 'Recent Sheets',
    noPayrollSheets: 'No payroll sheets found',
    createFirstSheet: 'Create first sheet',
    totalMonthly: 'Monthly Total',
    employees: 'Employees',
    companies: 'Companies',
    activeSheets: 'Active Sheets',
  },
  debug: {
    noCodesFound: 'No active codes found.',
  },

  // News System
  newsSystem: {
    title: 'News Center',
    subtitle: 'Instagram-style system',
    feed: 'News Feed',
    admin: 'Manage Posts',
    reminders: 'Reminders',
    acl: 'ACL Permissions',
    createPost: 'Create Post',
    newPost: 'New Post',
    whatAreYouThinking: 'What are you thinking? Share some news...',
    photoVideo: 'Photo/Video',
    event: 'Event',
    featured: 'Featured',
    selectMedia: 'Select photos and videos',
    selectMediaDesc: 'Share up to 10 photos or videos',
    selectFromComputer: 'Select from computer',
    edit: 'Edit',
    newPublication: 'New publication',
    sharing: 'Sharing...',
    advance: 'Next',
    share: 'Share',
    writeCaption: 'Write a caption...',
    addLocation: 'Add location',
    tagPeople: 'Tag people',
    audienceSettings: 'Audience settings',
    publicationShared: 'Publication shared!',
    publicationSharedDesc: 'Your publication was shared successfully',
    processing: 'Please wait while your publication is being processed',
    likes: 'likes',
    comments: 'comments',
    views: 'views',
    categories: {
      comunicados: 'Announcements',
      noticias: 'News',
      eventos: 'Events',
      treinamentos: 'Training',
      beneficios: 'Benefits',
      tecnologia: 'Technology'
    },
    filters: {
      allCategories: 'All categories',
      featuredOnly: 'Featured only'
    },
    stats: {
      systemFunctional: 'System Functional',
      tablesCreated: 'Tables Created',
      aclPermissions: 'ACL Permissions',
      notifications: 'Notifications'
    }
  },

  // ACL System
  acl: {
    title: 'ACL Management',
    subtitle: 'Advanced hierarchical access control',
    permissions: 'Permissions',
    roles: 'Roles',
    users: 'Users',
    managePermissions: 'Manage ACL permissions',
    configureRoles: 'Configure permissions by role',
    individualPermissions: 'Individual permissions',
    newPermission: 'New Permission',
    permissionName: 'Permission Name',
    description: 'Description',
    resource: 'Resource',
    action: 'Action',
    hierarchicalLevel: 'Hierarchical Level',
    level0: 'Level 0 - Basic (USER)',
    level1: 'Level 1 - Intermediate',
    level2: 'Level 2 - Advanced (MANAGER)',
    level3: 'Level 3 - Administrative (ADMIN)',
    createPermission: 'Create Permission',
    permissionsByRole: 'Permissions by Role',
    userManagement: 'User Management',
    userManagementDesc: 'To manage individual user permissions, access the user management panel.',
    goToUserManagement: 'Go to User Management',
    resources: {
      news: 'News',
      comments: 'Comments',
      notifications: 'Notifications',
      reminders: 'Reminders',
      admin: 'Administration'
    },
    actions: {
      read: 'Read',
      create: 'Create',
      update: 'Update',
      delete: 'Delete',
      publish: 'Publish',
      moderate: 'Moderate',
      send: 'Send',
      manage: 'Manage'
    }
  },

  // Notifications System
  notifications: {
    title: 'Notifications',
    markAsRead: 'Mark as read',
    markAllAsRead: 'Mark all as read',
    noNotifications: 'No notifications',
    unreadCount: '{count} unread',
    types: {
      info: 'Information',
      warning: 'Warning',
      error: 'Error',
      success: 'Success'
    },
    priorities: {
      low: 'Low',
      normal: 'Normal',
      high: 'High',
      urgent: 'Urgent'
    }
  },

  // Reminders System
  reminders: {
    title: 'Reminders',
    create: 'Create Reminder',
    manage: 'Manage Reminders',
    reminderTitle: 'Reminder Title',
    message: 'Message',
    remindAt: 'Remind at',
    targetRoles: 'Target Roles',
    targetUsers: 'Target Users',
    status: 'Status',
    pending: 'Pending',
    sent: 'Sent',
    cancelled: 'Cancelled',
    processing: 'Processing reminders...',
    statistics: 'Processing Statistics'
  },

  locale: {
    code: 'en-US'
  }
};
